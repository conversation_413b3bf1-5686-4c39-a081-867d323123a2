functions:

  lambda_authorizer:
    name: local-lambda_authorizer
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 128
    timeout: 180
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
    role: LambdaAuthorizerRole
    package:
      individually: true
      artifact: artifacts/lambda_authorizer.zip
      patterns:
        - "!**/*"
        - src/lambda_authorizer/**
  
  bre_handler:
    name: local-bre_handler
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
      BRE_LAMBDA: ${self:provider.stage}-bre
      BRE_LAMBDA_PRE_INVENTORY: ${self:provider.stage}-bre_pre_inventory
      BRE_LAMBDA_USED_CARS: ${self:provider.stage}-bre_used_cars
      LLM_LAMBDA: ${self:provider.stage}-llm_extractor
      LLM_LAMBDA_PRE_INVENTORY: ${self:provider.stage}-llm_extractor_pre_inventory
      LLM_LAMBDA_USED_CARS: ${self:provider.stage}-llm_extractor_used_cars
      ARIA_APP_ID_POST_INVENTORY: 73108b73-7960-4039-b5bf-bbc11cc606be
      ARIA_APP_ID_BOLS: 4ab7fb3c-2247-459e-85b5-cdb6dc9c19fe
      ARIA_APP_ID_TITLES: 09ccde48-4ed6-46e0-8ab4-174ca0484919
      ARIA_APP_ID_PRE_INVENTORY: 
      ARIA_APP_ID_USED_CARS: 9459a3e3-dcee-43ca-b46a-71857d605ba6
    role: BreHandlerRole
    package:
      individually: true
      artifact: artifacts/bre_handler.zip
      patterns:
        - "!**/*"
        - src/bre_handler/**

  bre:
    name: local-bre
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 512
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: BreRole
    package:
      individually: true
      artifact: artifacts/bre.zip
      patterns:
        - "!**/*"
        - src/bre/**

  email_watcher:
    name: local-email_watcher
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: EmailWatcherRole
    package:
      individually: true
      artifact: artifacts/email_watcher.zip
      patterns:
        - "!**/*"
        - src/email_watcher/**

  move_email:
    name: local-move_email
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
    role: MoveEmailRole
    package:
      individually: true
      artifact: artifacts/move_email.zip
      patterns:
        - "!**/*"
        - src/move_email/**

  process_email:
    name: local-process_email
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 512
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: ProcessEmailRole
    package:
      individually: true
      artifact: artifacts/process_email.zip
      patterns:
        - "!**/*"
        - src/process_email/**

  llm_messenger:
    name: local-llm_messenger
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
    role: LlmMessengerRole
    package:
      individually: true
      artifact: artifacts/llm_messenger.zip
      patterns:
        - "!**/*"
        - src/llm_messenger/**

  pdf_utils:
    name: local-pdf_utils
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
    role: PdfUtilsRole
    package:
      individually: true
      artifact: artifacts/pdf_utils.zip
      patterns:
        - "!**/*"
        - src/pdf_utils/**

  llm_extractor:
    name: local-llm_extractor
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 1024
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: LlmExtractorRole
    package:
      individually: true
      artifact: artifacts/llm_extractor.zip
      patterns:
        - "!**/*"
        - src/llm_extractor/**

  reynols_report_processor:
    name: local-reynols_report_processor
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 512
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: ReynolsReportProcessorRole
    package:
      individually: true
      artifact: artifacts/reynols_report_processor.zip
      patterns:
        - "!**/*"
        - src/reynols_report_processor/**

  invoice_downloader:
    name: local-invoice_downloader
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: InvoiceDownloaderRole
    package:
      individually: true
      artifact: artifacts/invoice_downloader.zip
      patterns:
        - "!**/*"
        - src/invoice_downloader/**

  invoice_to_aria:
    name: local-invoice_to_aria
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 512
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: InvoiceToAriaRole
    package:
      individually: true
      artifact: artifacts/invoice_to_aria.zip
      patterns:
        - "!**/*"
        - src/invoice_to_aria/**

  title_sftp_to_s3_to_aria:
    name: local-title_sftp_to_s3_to_aria
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 512
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: TitleSftpToS3ToAriaRole
    package:
      individually: true
      artifact: artifacts/title_sftp_to_s3_to_aria.zip
      patterns:
        - "!**/*"
        - src/title_sftp_to_s3_to_aria/**

  reconciliate:
    name: local-reconciliate
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 512
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: ReconciliateRole
    package:
      individually: true
      artifact: artifacts/reconciliate.zip
      patterns:
        - "!**/*"
        - src/reconciliate/**

  orchestrator_downloader:
    name: local-orchestrator_downloader
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: OrchestratorDownloaderRole
    package:
      individually: true
      artifact: artifacts/orchestrator_downloader.zip
      patterns:
        - "!**/*"
        - src/orchestrator_downloader/**

  orchestrator_download_update:
    name: local-orchestrator_download_update
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: OrchestratorDownloadUpdateRole
    package:
      individually: true
      artifact: artifacts/orchestrator_download_update.zip
      patterns:
        - "!**/*"
        - src/orchestrator_download_update/**

  reevaluate:
    name: local-reevaluate
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 512
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: ReevaluateRole
    package:
      individually: true
      artifact: artifacts/reevaluate.zip
      patterns:
        - "!**/*"
        - src/reevaluate/**

  python_handler:
    name: local-python_handler
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
    role: PythonHandlerRole
    package:
      individually: true
      artifact: artifacts/python_handler.zip
      patterns:
        - "!**/*"
        - src/python_handler/**

  loading_wi_report:
    name: local-loading_wi_report
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
    role: LoadingWiReportRole
    package:
      individually: true
      artifact: artifacts/loading_wi_report.zip
      patterns:
        - "!**/*"
        - src/loading_wi_report/**

  error_wi_report:
    name: local-error_wi_report
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: ErrorWiReportRole
    package:
      individually: true
      artifact: artifacts/error_wi_report.zip
      patterns:
        - "!**/*"
        - src/error_wi_report/**

  report_loaded_data:
    name: local-report_loaded_data
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
    role: ReportLoadedDataRole
    package:
      individually: true
      artifact: artifacts/report_loaded_data.zip
      patterns:
        - "!**/*"
        - src/report_loaded_data/**

  queues_handler:
    name: local-queues_handler
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: QueuesHandlerRole
    package:
      individually: true
      artifact: artifacts/queues_handler.zip
      patterns:
        - "!**/*"
        - src/queues_handler/**

  report_pages_not_used_in_titles:
    name: local-report_pages_not_used_in_titles
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
    role: ReportPagesNotUsedInTitlesRole
    package:
      individually: true
      artifact: artifacts/report_pages_not_used_in_titles.zip
      patterns:
        - "!**/*"
        - src/report_pages_not_used_in_titles/**

  secrets_handler:
    name: local-secrets_handler
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
    role: SecretsHandlerRole
    package:
      individually: true
      artifact: artifacts/secrets_handler.zip
      patterns:
        - "!**/*"
        - src/secrets_handler/**

  report_to_aria:
    name: local-report_to_aria
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 512
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: ReportToAriaRole
    package:
      individually: true
      artifact: artifacts/report_to_aria.zip
      patterns:
        - "!**/*"
        - src/report_to_aria/**

  load_pricing_guide:
    name: local-load_pricing_guide
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
    role: LoadPricingGuideRole
    package:
      individually: true
      artifact: artifacts/load_pricing_guide.zip
      patterns:
        - "!**/*"
        - src/load_pricing_guide/**

  load_pricing_guide_extractor:
    name: local-load_pricing_guide_extractor
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 1024
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
    role: LoadPricingGuideExtractorRole
    package:
      individually: true
      artifact: artifacts/load_pricing_guide_extractor.zip
      patterns:
        - "!**/*"
        - src/load_pricing_guide_extractor/**

  bre_used_cars:
    name: local-bre_used_cars
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 512
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: BreUsedCarsRole
    package:
      individually: true
      artifact: artifacts/bre_used_cars.zip
      patterns:
        - "!**/*"
        - src/bre_used_cars/**

  llm_extractor_used_cars:
    name: local-llm_extractor_used_cars
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 1024
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: LlmExtractorUsedCarsRole
    package:
      individually: true
      artifact: artifacts/llm_extractor_used_cars.zip
      patterns:
        - "!**/*"
        - src/llm_extractor_used_cars/**

  pre_stock_in_vins:
    name: local-pre_stock_in_vins
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: PreStockInVinsRole
    package:
      individually: true
      artifact: artifacts/pre_stock_in_vins.zip
      patterns:
        - "!**/*"
        - src/pre_stock_in_vins/**

  llm_extractor_pre_inventory:
    name: local-llm_extractor_pre_inventory
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 1024
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: LlmExtractorPreInventoryRole
    package:
      individually: true
      artifact: artifacts/llm_extractor_pre_inventory.zip
      patterns:
        - "!**/*"
        - src/llm_extractor_pre_inventory/**

  bre_pre_inventory:
    name: local-bre_pre_inventory
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 512
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: local_hennessy
    role: BrePreInventoryRole
    package:
      individually: true
      artifact: artifacts/bre_pre_inventory.zip
      patterns:
        - "!**/*"
        - src/bre_pre_inventory/**
